package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	util "socks/client/util"
)

// Message format for TCP communication
type Message struct {
	ID   string `json:"id"`
	Type string `json:"type"`
	Data []byte `json:"data,omitempty"`
}

// URLProxyMessage URL代理消息结构
type URLProxyMessage struct {
	ID        string            `json:"id"`                   // 请求 ID
	Type      string            `json:"type"`                 // proxy_request, proxy_response
	BaseURL   string            `json:"base_url,omitempty"`   // URL路径
	TargetURL string            `json:"target_url,omitempty"` // 目标URL
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息
}

// SafeConn now wraps a TCP connection instead of WebSocket
type SafeConn struct {
	Conn net.Conn
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器
}

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	if bw, ok := c.Conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	return err
}

func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

func (c *SafeConn) Close() error {
	return c.Conn.Close()
}

// Config 配置信息
type Config struct {
	HostName   string
	ServerIP   string
	ServerPort int
	LocalHost  string
	APIPort    int
	UUID       string
	Type       string
	Group      string
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	conns map[string]net.Conn
	mu    sync.Mutex
}

type UrlMapping struct {
	Host string
	Port int
}

// PortMapping updated to use TCP connection
type PortMapping struct {
	LocalPort  int                // Local port
	RemotePort int                // Remote port
	Conn       *SafeConn          // TCP connection
	CM         *ConnectionManager // Connection manager
	Created    time.Time          // Creation time
}

type URLRegisterRequest struct {
	ApiType      string `json:"api_type"`      // Agent, AI, or API
	ServiceGroup string `json:"service_group"` // 服务组别
	ServiceName  string `json:"service_name"`  // 服务名称
	AppName      string `json:"app_name"`      // 应用名称
	ClientUUID   string `json:"client_id"`     // 客户端UUID
	BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
}

type URLRegisterResponse struct {
	Success bool   `json:"success"`
	URLPath string `json:"url_path"`
}

// 全局映射管理
var (
	// 存储所有活跃的端口映射
	portMappings    = make(map[int]*PortMapping) // 本地端口 -> 映射信息
	portMappingsMux sync.Mutex

	urlMappings    = make(map[string]*UrlMapping)
	urlMappingsMux sync.Mutex
)

// 全局控制连接
var (
	globalConn *SafeConn

	// 全局连接管理器
	globalCM *ConnectionManager

	globalPortMappings    = make(map[int]*PortMapping) // 服务端口 -> 映射信息
	globalPortMappingsMux sync.RWMutex
)

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		conns: make(map[string]net.Conn),
	}
}

// Add 添加连接
func (cm *ConnectionManager) Add(id string, conn net.Conn) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.conns[id] = conn
}

// Remove 移除连接
func (cm *ConnectionManager) Remove(id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if conn, ok := cm.conns[id]; ok {
		conn.Close()
		delete(cm.conns, id)
	}
}

// Get 获取连接
func (cm *ConnectionManager) Get(id string) net.Conn {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	return cm.conns[id]
}

// parseFlags 解析命令行参数
func parseFlags() (*Config, error) {
	config := &Config{
		HostName: util.GetHostName(),
		UUID:     util.GenerateUUID(),
	}
	flag.StringVar(&config.ServerIP, "server", "", "中转服务器IP")
	flag.StringVar(&config.LocalHost, "host", "127.0.0.1", "本地服务IP")
	flag.StringVar(&config.Type, "type", "ai", "代理客户端类型")
	flag.StringVar(&config.Group, "group", "defaultGroup", "代理客户端组别")
	flag.StringVar(&config.HostName, "name", "defaultName", "代理客户端名称")
	flag.IntVar(&config.ServerPort, "port", 8090, "中转服务器端口")
	flag.IntVar(&config.APIPort, "manager", 8090, "代理客户端API服务端口")
	flag.Parse()

	if config.ServerIP == "" || config.ServerPort == 0 {
		return nil, fmt.Errorf("required parameters not provided")
	}
	return config, nil
}

// registerURLMapping 向服务器注册URL映射
func registerURLMapping(config *Config, baseURL, appName, serviceName, serviceGroup, apiType string) (*URLRegisterResponse, error) {
	// 构建请求体

	requestBody := URLRegisterRequest{
		ApiType:      apiType,
		ServiceGroup: serviceGroup,
		ServiceName:  serviceName,
		AppName:      appName,
		ClientUUID:   config.UUID,
		BaseURL:      baseURL,
	}

	requestData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("json encode failed: %v", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("http://%s:%d/url/register",
		config.ServerIP, config.ServerPort)

	// 发送POST请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("register url mapping failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("register url mapping failed: %s", string(body))
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read server response body failed: %w", err)
	}
	// 解析响应体
	var response *URLRegisterResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		// JSON parse failed
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	log.Printf("URL mapping registered successfully: %s", response.URLPath)
	return response, nil
}

// unregisterURLMapping 向服务器取消注册URL映射
func unregisterURLMapping(config *Config, urlPath string) error {
	// Build request URL
	url := fmt.Sprintf("http://%s:%d/url/unregister?client_id=%s&url_path=%s",
		config.ServerIP, config.ServerPort, config.UUID, urlPath)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create unregister request: %v", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to unregister URL mapping: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to unregister URL mapping: %s", string(body))
	}

	log.Printf("URL mapping unregistered successfully: %s", urlPath)
	return nil
}

func register(config *Config) (*SafeConn, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("Failed to get local IP: %v, using default IP", err)
	}

	// 构建请求URL
	serverAddr := fmt.Sprintf("%s:%d", config.ServerIP, config.ServerPort)
	url := fmt.Sprintf("http://%s/register?name=%s&type=%s&id=%s&ip=%s&group=%s",
		serverAddr, config.HostName, config.Type, config.UUID, localIP, config.Group)

	log.Printf("Connecting to server: %s", url)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %v", err)
	}

	// 创建TCP连接
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("connect failed: %v", err)
	}

	// 发送HTTP请求
	err = req.Write(conn)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("send HTTP request failed: %v", err)
	}

	// 读取HTTP响应头
	resp, err := http.ReadResponse(bufio.NewReader(conn), req)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("read response failed: %v", err)
	}
	log.Printf("sever response: %v", resp)
	// 检查响应状态
	if resp.StatusCode != http.StatusSwitchingProtocols {
		body, _ := io.ReadAll(resp.Body)
		conn.Close()
		return nil, fmt.Errorf("unexpected status code: %s, response body: %s", resp.Status, string(body))
	}

	// 设置TCP连接参数
	tcpConn := conn.(*net.TCPConn)
	tcpConn.SetKeepAlive(true)
	tcpConn.SetKeepAlivePeriod(30 * time.Second)
	tcpConn.SetWriteBuffer(32 * 1024)
	tcpConn.SetReadBuffer(32 * 1024)
	// 创建安全连接，使用更大的缓冲区
	bw := bufio.NewWriterSize(conn, 32*1024)
	safeConn := &SafeConn{
		Conn: conn,
		bw:   bw,
		enc:  json.NewEncoder(bw),
		dec:  json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
	}

	buf := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(5 * time.Second)) // 设置5秒超时
	_, err = conn.Read(buf)
	if err != nil {
		log.Printf("read register confirm message failed: %v", err)
	}
	conn.SetReadDeadline(time.Time{}) // 清除超时设置
	log.Printf("successfully registered client and established control connection")

	return safeConn, nil
}

func buildTunnel(config *Config, localPort int, serviceName string) (int, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("Failed to get local IP: %v, using default IP", err)
	}

	// 构建请求URL，包含本机IP和服务名称
	url := fmt.Sprintf("http://%s:%d/allocate?id=%s&port=%d&service_name=%s",
		config.ServerIP, config.ServerPort, config.UUID, localPort, serviceName)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("allocate port failed: %v", err)
	}
	defer resp.Body.Close()

	// 解析服务端返回的端口信息
	var result struct {
		Port int `json:"port"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, fmt.Errorf("parse response failed: %v", err)
	}

	log.Printf("successfully allocated port mapping: local port %d -> server port %d (local ip: %s)",
		localPort, result.Port, localIP)

	return result.Port, nil
}

// handleLocalToWS 处理本地到WS的数据转发
func handleLocalToWS(id string, localConn net.Conn, conn *SafeConn) {
	buf := make([]byte, 4096)
	for {
		n, err := localConn.Read(buf)

		if n > 0 {
			conn.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
		}
		if err != nil {
			log.Printf("read from local conn error: %v", err)
			break
		}
	}
	conn.WriteJSON(Message{ID: id, Type: "close"})
}

// isHTTPResponseComplete 检测HTTP响应是否完整
func isHTTPResponseComplete(data []byte) bool {
	// 查找HTTP响应头结束标记
	headerEndIndex := bytes.Index(data, []byte("\r\n\r\n"))
	if headerEndIndex == -1 {
		// 还没有完整的HTTP头部
		return false
	}

	// 解析HTTP响应头
	headerPart := string(data[:headerEndIndex])
	lines := strings.Split(headerPart, "\r\n")

	var contentLength int = -1
	var isChunked bool = false
	var connectionClose bool = false

	for _, line := range lines {
		lowerLine := strings.ToLower(line)
		if strings.HasPrefix(lowerLine, "content-length:") {
			// 解析Content-Length
			parts := strings.Split(line, ":")
			if len(parts) == 2 {
				if length, err := strconv.Atoi(strings.TrimSpace(parts[1])); err == nil {
					contentLength = length
				}
			}
		} else if strings.HasPrefix(lowerLine, "transfer-encoding:") && strings.Contains(lowerLine, "chunked") {
			isChunked = true
		} else if strings.HasPrefix(lowerLine, "connection:") && strings.Contains(lowerLine, "close") {
			connectionClose = true
		}
	}

	bodyStart := headerEndIndex + 4
	bodyData := data[bodyStart:]

	// 检查不同的结束条件
	if contentLength >= 0 {
		// 有Content-Length，检查是否已读取完指定长度
		return len(bodyData) >= contentLength
	} else if isChunked {
		// Chunked编码，检查是否以0\r\n\r\n结尾
		return bytes.HasSuffix(data, []byte("0\r\n\r\n"))
	} else if connectionClose {
		// Connection: close，需要等待连接关闭
		return false // 这种情况下通过连接关闭来判断
	}

	// 默认情况，无法确定是否完整
	return false
}

// handleURLLocalToWS 处理URL代理本地到WS的数据转发
func handleURLLocalToWS(id string, localConn net.Conn, conn *SafeConn) {
	defer func() {
		// 确保在函数结束时清理资源
		globalCM.Remove(id)
		localConn.Close()
		log.Printf("URL proxy connection resources cleaned up: %s", id)
	}()

	log.Printf("Start URL proxy data forwarding: %s", id)

	// 转发响应体数据
	buf := make([]byte, 4096)
	totalBytes := 0
	chunkCount := 0
	var responseBuffer bytes.Buffer
	httpResponseComplete := false

	for {
		n, err := localConn.Read(buf)
		if n > 0 {
			totalBytes += n
			chunkCount++

			// 将数据添加到缓冲区用于HTTP响应检测
			responseBuffer.Write(buf[:n])

			// 检测HTTP响应是否完整
			if !httpResponseComplete {
				httpResponseComplete = isHTTPResponseComplete(responseBuffer.Bytes())
			}

			if writeErr := conn.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]}); writeErr != nil {
				log.Printf("Failed to forward response data: %s, %v", id, writeErr)
				break
			}
			// 如果HTTP响应已完整，发送关闭消息并退出
			if httpResponseComplete {
				log.Printf("Detected complete HTTP response: %s, total %d bytes forwarded", id, totalBytes)
				break
			}
		}
		if err != nil {
			if err == io.EOF {
				log.Printf("URL proxy response body read complete: %s, total %d bytes, %d chunks", id, totalBytes, chunkCount)
			} else if netErr, ok := err.(interface{ Timeout() bool }); ok && netErr.Timeout() {
				log.Printf("URL proxy response read timeout, no return from local connection: %s, %d bytes forwarded", id, totalBytes)
			} else {
				log.Printf("URL proxy response body read error: %s, %v, %d bytes forwarded", id, err, totalBytes)
			}
			break
		}
	}

	// 发送关闭消息给服务端，表示响应传输完成
	conn.WriteJSON(Message{ID: id, Type: "close"})
	log.Printf("URL proxy data forwarding ended: %s, total %d bytes, %d chunks", id, totalBytes, chunkCount)
}

// handleOpenMessage 处理打开连接消息
func handleOpenMessage(msg Message, conn *SafeConn, cm *ConnectionManager, localAddr string) {
	localConn, err := net.Dial("tcp", localAddr)
	if err != nil {
		log.Printf("Failed to connect to local service: %v", err)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}
	log.Printf("msg.ID: %s", msg.ID)
	cm.Add(msg.ID, localConn)
	log.Printf("Add local conn success")

	go handleLocalToWS(msg.ID, localConn, conn)
}

// handleURLOpenMessage 处理URL代理TCP连接打开消息
func handleURLOpenMessage(msg Message, conn *SafeConn, cm *ConnectionManager) {
	// 解析baseURL和targetPath
	data := string(msg.Data)
	parts := strings.Split(data, "|")
	if len(parts) != 2 {
		log.Printf("Invalid url_open message data: %s", data)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}

	baseURL := parts[0]
	targetPath := parts[1]

	log.Printf("Handling URL proxy connection: baseURL=%s, targetPath=%s", baseURL, targetPath)

	// 解析目标URL并建立连接
	var localConn net.Conn
	var err error
	var targetAddr string

	// 首先尝试查找URL映射
	urlMappingsMux.Lock()
	mapping, hasMappingConfig := urlMappings[baseURL]
	urlMappingsMux.Unlock()

	if !hasMappingConfig {
		// 使用映射配置连接到本地服务
		log.Printf("No URL mapping config found, resolving URL directly: %s", baseURL)
		return
	}

	targetAddr = fmt.Sprintf("%s:%d", mapping.Host, mapping.Port)
	localConn, err = net.Dial("tcp", targetAddr)
	log.Printf("Connecting to local service using mapping config: %s", targetAddr)
	if err != nil {
		log.Printf("Failed to connect to service: %s, %v", targetAddr, err)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}

	log.Printf("successfully connected to local service: %s", targetAddr)
	cm.Add(msg.ID, localConn)

	// 启动数据转发
	go handleURLLocalToWS(msg.ID, localConn, conn)
}

// handleDataMessage 处理数据消息
func handleDataMessage(msg Message, cm *ConnectionManager) {
	if localConn := cm.Get(msg.ID); localConn != nil {
		// 检查是否是URL代理连接
		if strings.HasPrefix(msg.ID, "url_") {
			// URL代理连接需要处理Host头
			handleURLDataMessage(msg, localConn)
		} else {
			// 普通端口代理连接直接转发
			localConn.Write(msg.Data)
		}
	}
}

// handleURLDataMessage 处理URL代理的数据消息，修改Host头和URL路径
func handleURLDataMessage(msg Message, localConn net.Conn) {
	// 解析HTTP请求数据
	requestData := string(msg.Data)

	// 分离请求头和请求体
	parts := strings.Split(requestData, "\r\n\r\n")
	if len(parts) < 1 {
		log.Printf("Invalid HTTP request data: %s", msg.ID)
		return
	}

	headerPart := parts[0]
	var bodyPart string
	if len(parts) > 1 {
		bodyPart = strings.Join(parts[1:], "\r\n\r\n")
	}

	// 解析请求头
	lines := strings.Split(headerPart, "\r\n")
	if len(lines) < 1 {
		log.Printf("Invalid HTTP request header: %s", msg.ID)
		return
	}

	// 解析请求行并修改URL路径
	requestLine := lines[0]
	requestParts := strings.Split(requestLine, " ")
	if len(requestParts) != 3 {
		log.Printf("Invalid HTTP request line: %s", requestLine)
		return
	}

	method := requestParts[0]
	originalURL := requestParts[1]
	protocol := requestParts[2]

	// 构建新的请求行
	newRequestLine := fmt.Sprintf("%s %s %s", method, originalURL, protocol)

	// 构建新的请求头，修改Host和URL
	var newHeaders []string
	newHeaders = append(newHeaders, newRequestLine)

	// 获取目标端口（从URL映射中获取）
	var targetPort int
	urlMappingsMux.Lock()
	for _, mapping := range urlMappings {
		targetPort = mapping.Port
		break // 使用第一个映射的端口
	}
	urlMappingsMux.Unlock()

	if targetPort == 0 {
		targetPort = 80 // 默认端口
	}

	// 添加修改后的Host头
	newHeaders = append(newHeaders, fmt.Sprintf("Host: localhost:%d", targetPort))

	// 添加其他请求头（跳过原始的Host头）
	for i := 1; i < len(lines); i++ {
		line := lines[i]
		if !strings.HasPrefix(strings.ToLower(line), "host:") {
			newHeaders = append(newHeaders, line)
		}
	}

	// 重新构建HTTP请求
	newRequest := strings.Join(newHeaders, "\r\n") + "\r\n\r\n" + bodyPart

	// log.Printf("修改Host头后的请求 (连接ID: %s):\n%s", msg.ID, newRequest)

	// 发送修改后的请求到本地连接
	if _, err := localConn.Write([]byte(newRequest)); err != nil {
		log.Printf("Failed to send request to local connection: %s, %v", msg.ID, err)
	}
}

// 重连函数 - 一次性调用，直至重连完成才返回
func reconnectUntilSuccess(config *Config) {
	log.Printf("connection lost, starting reconnect mechanism...")

	for {
		log.Printf("Trying to reconnect to server...")
		newConn, err := register(config)
		if err != nil {
			log.Printf("Reconnect failed: %v, will retry in 30 seconds", err)
			time.Sleep(30 * time.Second)
			continue
		}

		// 重连成功，更新全局连接
		log.Printf("Reconnect succeeded, updating global connection and connection manager")

		// 关闭旧连接（如果存在）
		if globalConn != nil {
			globalConn.Close()
		}

		// 更新全局连接
		globalConn = newConn

		// 关闭旧连接管理器中的所有tcp连接
		if globalCM != nil {
			globalCM.mu.Lock()
			for id, conn := range globalCM.conns {
				conn.Close()
				delete(globalCM.conns, id)
			}
			globalCM.mu.Unlock()
		}

		// 重新启动连接管理
		buildGlobalCM(config)

		log.Printf("Reconnect complete, service is back to normal")
		return // 重连成功，退出函数
	}
}

// 获取或创建全局控制连接
func buildGlobalCM(config *Config) {
	if globalCM == nil {
		globalCM = NewConnectionManager()
	}

	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			err := globalConn.WriteJSON(Message{
				ID:   util.HEARTBEAT,
				Type: "ping",
				Data: []byte("ping"),
			})
			if err != nil {
				log.Printf("Failed to send heartbeat: %v", err)
				return
			}
		}
	}()

	// 启动全局消息处理
	go func() {
		for {
			var msg Message
			if err := globalConn.ReadJSON(&msg); err != nil {
				log.Printf("Global connection read error: %v", err)
				// 启动重连，这个函数会阻塞直到重连成功
				go reconnectUntilSuccess(config)
				break
			}

			log.Printf("Received message: Type=%s, ID=%s", msg.Type, msg.ID)
			// Route to handler by message type and ID

			switch msg.Type {
			case "open":
				serverPort, err := util.GetServerPortByMsgId(msg.ID)
				if err != nil {
					log.Printf("Message process error: %v", err)
				}
				// 查找对应的本地端口
				globalPortMappingsMux.RLock()
				defer globalPortMappingsMux.RUnlock()
				var localAddr string
				if mapping, ok := globalPortMappings[serverPort]; ok {
					localAddr = fmt.Sprintf("0.0.0.0:%d", mapping.LocalPort)
					handleOpenMessage(msg, globalConn, globalCM, localAddr)
				} else {
					log.Printf("Server port mapping not built, server port: %d", serverPort)
				}
			case "url_open":
				// 处理URL代理TCP连接打开请求
				handleURLOpenMessage(msg, globalConn, globalCM)
			case "data":
				handleDataMessage(msg, globalCM)

			case "close":
				globalCM.Remove(msg.ID)
			case "ping":
				// 处理心跳消息
				globalConn.WriteJSON(Message{ID: msg.ID, Type: "pong"})
			}
		}
		log.Print("message process goroutine exited, connection lost")
	}()
}

// buildPortMapping 新的端口映射逻辑，复用全局控制连接和连接管理器
func buildPortMapping(config *Config, localPort int, serviceName string) (*PortMapping, error) {
	// 检查全局连接是否可用
	if globalConn == nil {
		return nil, fmt.Errorf("global connection not built, please wait for reconnect to complete")
	}

	// Check if mapping already exists
	portMappingsMux.Lock()
	defer portMappingsMux.Unlock()
	if mapping, exists := portMappings[localPort]; exists {
		return mapping, nil
	}

	// 申请远程端口
	remotePort, err := buildTunnel(config, localPort, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to apply for remote port: %v", err)
	}
	log.Printf("Successfully applied for port mapping: local port %d -> remote port %d", localPort, remotePort)

	// 创建映射记录，使用全局连接管理器
	mapping := &PortMapping{
		LocalPort:  localPort,
		RemotePort: remotePort,
		Conn:       globalConn, // 复用全局连接
		CM:         globalCM,   // 复用全局连接管理器
		Created:    time.Now(),
	}

	// 保存映射
	globalPortMappingsMux.Lock()
	globalPortMappings[remotePort] = mapping
	globalPortMappingsMux.Unlock()

	return mapping, nil
}

// startAPIServer 启动API服务器
func startAPIServer(config *Config) {

	// 状态端点
	http.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type MappingInfo struct {
			LocalPort  int       `json:"local_port"`
			RemotePort int       `json:"remote_port"`
			Created    time.Time `json:"created"`
		}

		var mappings []MappingInfo

		portMappingsMux.Lock()
		for _, mapping := range portMappings {
			mappings = append(mappings, MappingInfo{
				LocalPort:  mapping.LocalPort,
				RemotePort: mapping.RemotePort,
				Created:    mapping.Created,
			})
		}
		portMappingsMux.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"server":   fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort),
			"mappings": mappings,
			"count":    len(mappings),
		})
	})

	http.HandleFunc("/tunnel", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost && r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取本地端口
		portStr := r.URL.Query().Get("port")
		if portStr == "" {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		localPort := 0
		if _, err := fmt.Sscanf(portStr, "%d", &localPort); err != nil || localPort <= 0 || localPort > 65535 {
			http.Error(w, "Invalid port number", http.StatusBadRequest)
			return
		}

		serviceName := r.URL.Query().Get("service_name")

		// 设置映射
		mapping, err := buildPortMapping(config, localPort, serviceName)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回映射信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"local_port":  mapping.LocalPort,
			"remote_port": mapping.RemotePort,
			"server":      config.ServerIP,
			"created":     mapping.Created,
		})
	})

	// URL注册端点
	http.HandleFunc("/url/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type URLRegisterRequest struct {
			AppName      string `json:"app_name"`
			ServiceName  string `json:"service_name"`
			ServiceGroup string `json:"service_group"`
			ServicePort  int    `json:"service_port"`
			ApiType      string `json:"api_type"`
			BaseURL      string `json:"base_url"`
		}

		var req URLRegisterRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "illegal request body", http.StatusBadRequest)
			return
		}

		if req.AppName == "" {
			http.Error(w, "Missing name parameter", http.StatusBadRequest)
			return
		}

		if req.ServiceGroup == "" {
			http.Error(w, "Missing group parameter", http.StatusBadRequest)
			return
		}

		if req.ApiType == "" {
			http.Error(w, "Missing type parameter", http.StatusBadRequest)
			return
		}

		if req.ServicePort <= 0 || req.ServicePort > 65535 {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		urlMappingsMux.Lock()
		defer urlMappingsMux.Unlock()
		if _, ok := urlMappings[req.BaseURL]; ok {
			http.Error(w, "url already registed", http.StatusBadRequest)
			return
		}

		// 向server注册URL映射
		response, err := registerURLMapping(config, req.BaseURL, req.AppName, req.ServiceName, req.ServiceGroup, util.GetFormatServiceType(req.ApiType))
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		urlMappings[req.BaseURL] = &UrlMapping{Host: config.LocalHost, Port: req.ServicePort}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// URL取消注册端点
	http.HandleFunc("/url/unregister", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodDelete {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取参数
		baseURL := r.URL.Query().Get("url")
		if baseURL == "" {
			http.Error(w, "Missing url_path parameter", http.StatusBadRequest)
			return
		}

		// 向server取消注册URL映射
		err := unregisterURLMapping(config, baseURL)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":  true,
			"url_path": baseURL,
		})
	})

	// 启动API服务器
	apiAddr := fmt.Sprintf("%s:%d", config.LocalHost, config.APIPort)
	log.Printf("API server started at %s", apiAddr)
	if err := http.ListenAndServe(apiAddr, nil); err != nil {
		log.Fatalf("API server start failed: %v", err)
	}
}

func main() {
	// 解析命令行参数
	config, err := parseFlags()
	if err != nil {
		flag.Usage()
		os.Exit(1)
	}

	log.Printf("Registering client...")
	globalConn, err = register(config)
	if err != nil {
		log.Printf("Client registration error: %v", err)
		os.Exit(1)
	}
	log.Printf("Client registered successfully, building global connection manager...")

	buildGlobalCM(config)
	log.Printf("Global connection manager built, starting API server...")

	// 启动API服务器
	startAPIServer(config)
}
