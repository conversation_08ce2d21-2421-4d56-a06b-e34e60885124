package main

import (
    "encoding/json"
    "os"
)

// TunnelConfig 存储隧道相关配置
type TunnelConfig struct {
    ManagerPort        int `json:"IntranetTunnelManagerPort"`
    MinPort            int `json:"IntranetTunnelMinPort"`
    MaxPort            int `json:"IntranetTunnelMaxPort"`
    Timeout            int `json:"IntranetTunnelTimeout"`
    MaxConnection      int `json:"IntranetTunnelMaxConnection"`
    SlidingExpiration  int `json:"IntranetTunnelSlidingExpiration"`
}

// LoadTunnelConfig 从指定路径加载隧道配置
func LoadTunnelConfig(path string) (*TunnelConfig, error) {
    // 读取文件内容
    data, err := os.ReadFile(path)
    if err != nil {
        return nil, err
    }
    
    // 解析JSON
    var config struct {
        TunnelConfig
    }
    
    if err := json.Unmarshal(data, &config); err != nil {
        return nil, err
    }
    
    return &config.TunnelConfig, nil
}